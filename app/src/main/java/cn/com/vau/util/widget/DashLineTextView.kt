package cn.com.vau.util.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.text.TextUtils
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.withStyledAttributes
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px

/**
 * 带虚线下划线的TextView
 */
class DashLineTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    enum class DashAlignment {
        BASELINE,   // 基于基线
        DESCENT,    // 基于descent线（默认，适用于包含下降字符的文本）
        BOTTOM      // 基于bottom线
    }

    // 虚线属性
    private var dashColor: Int = AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff)
    private var dashStrokeWidth: Float = 1f.dp2px()
    private var dashLength: Float = 4f.dp2px()
    private var dashGap: Float = 4f.dp2px()

    // 虚线距离对齐线的偏移量
    private var dashOffset: Float = 2f.dp2px()

    // 默认使用DESCENT对齐
    private var dashAlignment: DashAlignment = DashAlignment.DESCENT
    private var dashEnabled: Boolean = true
    private var dashCap: Paint.Cap = Paint.Cap.ROUND

    private val dashPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
    }

    init {
        attrs?.let {
            context.withStyledAttributes(it, R.styleable.DashLineTextView) {

                dashColor = getColor(R.styleable.DashLineTextView_dlDashColor, dashColor)
                dashStrokeWidth = getDimension(R.styleable.DashLineTextView_dlDashStrokeWidth, dashStrokeWidth)
                dashLength = getDimension(R.styleable.DashLineTextView_dlDashLength, dashLength)
                dashGap = getDimension(R.styleable.DashLineTextView_dlDashGap, dashGap)
                dashOffset = getDimension(R.styleable.DashLineTextView_dlDashOffset, dashOffset)
                dashEnabled = getBoolean(R.styleable.DashLineTextView_dlDashEnabled, dashEnabled)

                val alignmentValue = getInt(R.styleable.DashLineTextView_dlDashAlignment, 0)
                dashAlignment = when (alignmentValue) {
                    0 -> DashAlignment.BASELINE
                    1 -> DashAlignment.DESCENT
                    2 -> DashAlignment.BOTTOM
                    else -> DashAlignment.DESCENT
                }

                val capValue = getInt(R.styleable.DashLineTextView_dlDashCap, 0)
                dashCap = when (capValue) {
                    0 -> Paint.Cap.ROUND
                    1 -> Paint.Cap.SQUARE
                    else -> Paint.Cap.ROUND
                }

            }
        }

        updateDashPaint()
    }

    private fun updateDashPaint() {
        dashPaint.apply {
            color = dashColor
            strokeWidth = dashStrokeWidth
            strokeCap = dashCap
            pathEffect = DashPathEffect(floatArrayOf(dashLength, dashGap), 0f)
        }
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (!dashEnabled || layout == null || TextUtils.isEmpty(text)) {
            return
        }
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        drawDashedUnderlines(canvas)
    }

    private fun drawDashedUnderlines(canvas: Canvas) {
        val layout = layout ?: return
        val lineCount = layout.lineCount

        val compoundPaddingLeft = compoundPaddingLeft.toFloat()
        val extendedPaddingTop = extendedPaddingTop.toFloat()

        for (i in 0 until lineCount) {
            val lineLeft = layout.getLineLeft(i) + compoundPaddingLeft
            val lineRight = layout.getLineRight(i) + compoundPaddingLeft
            val lineBaseline = layout.getLineBaseline(i).toFloat() + extendedPaddingTop
            val dashY = calculateDashY(lineBaseline, i)
            val adjustedLeft = lineLeft + calculateLetterSpacingOffset()
            val adjustedRight = lineRight - calculateLetterSpacingOffset()
            canvas.drawLine(adjustedLeft, dashY, adjustedRight, dashY, dashPaint)
        }
    }

    private fun calculateDashY(baseline: Float, lineIndex: Int): Float {
        val fontMetrics = paint.fontMetricsInt

        return when (dashAlignment) {
            DashAlignment.BASELINE -> {
                baseline + maxOf(dashOffset, 2f.dp2px())
            }

            DashAlignment.DESCENT -> {
                baseline + fontMetrics.descent + dashOffset
            }

            DashAlignment.BOTTOM -> {
                baseline + fontMetrics.bottom + dashOffset
            }
        }
    }

    private fun calculateLetterSpacingOffset(): Float {
        // 根据字母间距调整
        return letterSpacing * textSize * 0.1f
    }

    /**
     * 设置虚线颜色
     */
    fun setDashColor(@ColorInt color: Int) {
        if (dashColor != color) {
            dashColor = color
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线线条宽度
     */
    fun setDashStrokeWidth(width: Float) {
        if (dashStrokeWidth != width) {
            dashStrokeWidth = width
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线长度
     */
    fun setDashLength(length: Float) {
        if (dashLength != length) {
            dashLength = length
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线间隔
     */
    fun setDashGap(gap: Float) {
        if (dashGap != gap) {
            dashGap = gap
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线距离对齐线的偏移量
     */
    fun setDashOffset(offset: Float) {
        if (dashOffset != offset) {
            dashOffset = offset
            invalidate()
        }
    }

    /**
     * 设置虚线对齐方式
     */
    fun setDashAlignment(alignment: DashAlignment) {
        if (dashAlignment != alignment) {
            dashAlignment = alignment
            invalidate()
        }
    }

    /**
     * 启用或禁用虚线
     */
    fun setDashEnabled(enabled: Boolean) {
        if (dashEnabled != enabled) {
            dashEnabled = enabled
            invalidate()
        }
    }

    /**
     * 设置虚线端点样式
     */
    fun setDashCap(cap: Paint.Cap) {
        if (dashCap != cap) {
            dashCap = cap
            updateDashPaint()
            invalidate()
        }
    }

    // Getter方法
    fun getDashColor(): Int = dashColor
    fun getDashStrokeWidth(): Float = dashStrokeWidth
    fun getDashLength(): Float = dashLength
    fun getDashGap(): Float = dashGap
    fun getDashOffset(): Float = dashOffset
    fun getDashAlignment(): DashAlignment = dashAlignment
    fun isDashEnabled(): Boolean = dashEnabled
    fun getDashCap(): Paint.Cap = dashCap
}
